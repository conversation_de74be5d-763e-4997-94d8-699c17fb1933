{"Landing page": "Landing page", "This ad will appear on shared file preview page.": "This ad will appear on shared file preview page.", "This ad will appear on user drive page.": "This ad will appear on user drive page.", "This ad will appear at the top of the landing page.": "This ad will appear at the top of the landing page.", "View features": "View features", "Drop files to upload them to this folder.": "Drop files to upload them to this folder.", "Landing Page": "<PERSON>", "Action buttons": "Action buttons", "Primary features": "Primary features", "Secondary features": "Secondary features", "Shareable link page": "Shareable link page", "Drive": "Drive", "Configure defaults for drive user dashboard.": "Configure defaults for drive user dashboard.", "Default view mode": "Default view mode", "Which view mode should user drive use by default.": "Which view mode should user drive use by default.", "List": "List", "Grid": "Grid", "Send a notification to user when a file or folder is shared with them.": "Send a notification to user when a file or folder is shared with them.", "Share notifications": "Share notifications", "Suggest email address of existing users when sharing a file or folder.": "Suggest email address of existing users when sharing a file or folder.", "Suggest emails": "Suggest emails", "Who has access": "Who has access", "Manage Access": "Manage Access", "Properties": "Properties", "Type": "Type", "Size": "Size", "Location": "Location", "Root": "Root", "Owner": "Owner", "Modified": "Modified", "Created": "Created", "Tags": "Tags", "+Add tag": "+Add tag", "All files": "All files", "Select file or folder to see details here": "Select file or folder to see details here", "More actions": "More actions", "New folder": "New folder", "Upload files": "Upload files", "Upload folder": "Upload folder", "Empty trash": "Empty trash", "Preview": "Preview", "Share": "Share", "Get link": "Get link", "Add to starred": "Add to starred", "Remove from starred": "Remove from starred", "Move to": "Move to", "Rename": "<PERSON><PERSON>", "Make a copy": "Make a copy", "Download": "Download", "Delete forever": "Delete forever", "Remove": "Remove", "Restore": "Rest<PERSON>", "Removed [one 1 item|other {count} items]": "Removed [one 1 item|other {count} items]", "Could not remove items": "Could not remove items", "Drop files or folders here": "Drop files or folders here", "Or use the \"Upload\" button": "Or use the \"Upload\" button", "Recent": "Recent", "No recent entries": "No recent entries", "You have not uploaded any files or folders yet": "You have not uploaded any files or folders yet", "Search results": "Search results", "No matching results": "No matching results", "Try changing your search query or filters": "Try changing your search query or filters", "Begin typing or select a filter to search": "Begin typing or select a filter to search", "Search for files, folders and other content": "Search for files, folders and other content", "Shared": "Shared", "Shared with me": "Shared with me", "Files and folders other people have shared with you": "Files and folders other people have shared with you", "Trash": "Trash", "Trash is empty": "Trash is empty", "There are no files or folders in your trash currently": "There are no files or folders in your trash currently", "Starred": "Starred", "Nothing is starred": "Nothing is starred", "Add stars to files and folders that you want to easily find later": "Add stars to files and folders that you want to easily find later", "Upload": "Upload", "Create folder": "Create folder", "List view": "List view", "Grid view": "Grid view", "Hide details": "Hide details", "Show details": "Show details", ":count selected": ":count selected", "User file was uploaded by": "User file was uploaded by", "anyone": "anyone", "me": "me", "not me": "not me", "Date file was uploaded": "Date file was uploaded", "Date file was last changed": "Date file was last changed", "In trash": "In trash", "Only show files that are in the trash": "Only show files that are in the trash", "Has shareable link": "Has shareable link", "Only show files that have a shareable link": "Only show files that have a shareable link", "Shared by me": "Shared by me", "Only show files that are shared with someone": "Only show files that are shared with someone", "Search": "Search", "Search files and folders": "Search files and folders", "Type to search": "Type to search", "Member removed": "Member removed", "Could not remove member": "Could not remove member", "Can view": "Can view", "Can Download": "Can Download", "Can edit": "Can edit", "Share ‘:name’": "Share ‘:name’", "Not a valid email": "Not a valid email", "Enter email addresses": "Enter email addresses", "Invite people": "Invite people", "Upload complete": "Upload complete", "Upload cancelled": "Upload cancelled", "Upload failed": "Upload failed", ":bytesUploaded of :totalBytes": ":bytesUploaded of :totalBytes", "This file could not be uploaded": "This file could not be uploaded", "Uploading :count files": "Uploading :count files", "Uploaded :count files": "Uploaded :count files", "No active uploads": "No active uploads", "You have exhausted your allowed space of :space. Delete some files or upgrade your plan.": "You have exhausted your allowed space of :space. Delete some files or upgrade your plan.", "Upgrade": "Upgrade", "This folder is in your trash": "This folder is in your trash", "To view this folder, restore it from the trash.": "To view this folder, restore it from the trash.", "‘:name‘ will be deleted forever and you won't be able to restore it.": "‘:name‘ will be deleted forever and you won't be able to restore it.", ":count items will be deleted forever and you won't be able to restore them.": ":count items will be deleted forever and you won't be able to restore them.", "Delete forever?": "Delete forever?", "Untitled Folder": "Untitled Folder", "Folder created": "Folder created", "New Folder": "New Folder", "Enter a name...": "Enter a name...", "Cancel": "Cancel", "Create": "Create", "Save": "Save", "Starred [one 1 item|other :count items]": "Starred [one 1 item|other :count items]", "Could not star items": "Could not star items", "Emptied trash": "Emptied trash", "Permanently deleted [one 1 item|other :count items]": "Permanently deleted [one 1 item|other :count items]", "Moved [one 1 item|other :count items] to trash": "Moved [one 1 item|other :count items] to trash", "Emptying trash...": "Emptying trash...", "Deleting files...": "Deleting files...", "Moving to trash...": "Moving to trash...", "Duplicating [one 1 item|other :count items]...": "Duplicating [one 1 item|other :count items]...", "Duplicated [one 1 item|other :count items]": "Duplicated [one 1 item|other :count items]", "Could not duplicate items": "Could not duplicate items", "Moving [one 1 item|other :count items]...": "Moving [one 1 item|other :count items]...", "Moved [one 1 item|other :count items] to \":destination\"": "Moved [one 1 item|other :count items] to \":destination\"", "Could not move items": "Could not move items", "Removed star from [one 1 item|other :count items]": "Removed star from [one 1 item|other :count items]", "Could not remove star": "Could not remove star", ":oldName renamed to :newName": ":oldName renamed to :newName", "Restored [one 1 item|other :count items]": "Restored [one 1 item|other :count items]", "Could not restore items": "Could not restore items", "Failed to save tags.": "Failed to save tags.", "Name": "Name", "Last modified": "Last modified", "Actions": "Actions", "Upload date": "Upload date", "Extension": "Extension", "Direction": "Direction", "Ascending": "Ascending", "Descending": "Descending", "Sort By": "Sort By", "Shareable Link Settings": "Shareable Link Settings", "Link settings saved": "Link settings saved", "Allow download": "Allow download", "Users with link can download this item": "Users with link can download this item", "Allow import": "Allow import", "Users with link can import this item into their own drive": "Users with link can import this item into their own drive", "Link expiration": "Link expiration", "Link is valid until": "Link is valid until", "Link expiration date and time": "Link expiration date and time", "Password protect": "Password protect", "Users will need to enter password in order to view this link": "Users will need to enter password in order to view this link", "Link password": "Link password", "Password will not be requested when viewing the link as file owner.": "Password will not be requested when viewing the link as file owner.", "Enter new password...": "Enter new password...", "Share link": "Share link", "Shareable link is created": "Shareable link is created", "Create shareable link": "Create shareable link", "Link settings": "Link settings", "Shareable link": "Shareable link", "Copied!": "Copied!", "Copy": "Copy", "Updated user permissions": "Updated user permissions", "Could not update permissions": "Could not update permissions", "Could not create link": "Could not create link", "Could not delete link": "Could not delete link", "Header button 1": "Header button 1", "Header button 2": "Header button 2", "Footer button": "Footer button", "Header title": "Header title", "Header subtitle": "Header subtitle", "Header image": "Header image", "Header image opacity": "Header image opacity", "In order for overlay colors to appear, header image opacity will need to be less then 100%": "In order for overlay colors to appear, header image opacity will need to be less then 100%", "Header overlay color 1": "Header overlay color 1", "Header overlay color 2": "Header overlay color 2", "Footer title": "Footer title", "Footer subtitle": "Footer subtitle", "Footer background image": "Footer background image", "Add feature": "Add feature", "Image": "Image", "Title": "Title", "Subtitle": "Subtitle", "Description": "Description", "Search folders": "Search folders", "Move [one ‘:name‘|other :count items]": "Move [one ‘:name‘|other :count items]", "Select a destination folder.": "Select a destination folder.", "Move here": "Move here", ":used of :available used": ":used of :available used", "Password": "Password", "The link you are trying to access is password protected.": "The link you are trying to access is password protected.", "Password is not valid": "Password is not valid", "Enter": "Enter", "Save a copy to your own drive": "Save a copy to your own drive", "Loading link": "Loading link", "Folder is empty": "Folder is empty", "No files have been uploaded to this folder yet": "No files have been uploaded to this folder yet", "Item imported into your drive": "Item imported into your drive", "Version: :number": "Version: :number", "Back": "Back", "Flat color": "Flat color", "Gradient": "Gradient", "Custom color": "Custom color", "Custom gradient": "Custom gradient", "Colors": "Colors", "Apply": "Apply", "Click to change color": "Click to change color", "Worm flame": "Worm flame", "Night fade": "Night fade", "Winter nova": "Winter nova", "Heavy rain": "Heavy rain", "Cloudy knoxville": "Cloudy knoxville", "Rare wind": "Rare wind", "Saint petersburg": "Saint petersburg", "Everlasting sky": "Everlasting sky", "Soft grass": "Soft grass", "Delicate": "Delicate", "Broken hearts": "Broken hearts", "Lush": "<PERSON><PERSON>", "Ash": "Ash", "Clouds": "Clouds", "Mango pulp": "Mango pulp", "Cooper": "<PERSON>", "Vicious stance": "Vicious stance", "Custom image": "Custom image", "Protruding squares": "Protruding squares", "Launch day": "Launch day", "Alternating triangles": "Alternating triangles", "Monstera patch": "Monstera patch", "Confetti doodles": "Confetti doodles", "Hurricane aperture": "Hurricane aperture", "Looney loops": "Looney loops", "Icy explosion": "Icy explosion", "Nuclear point": "Nuclear point", "Angled focus": "Angled focus", "Circular focus": "Circular focus", "Farseeing eyeball": "Farseeing eyeball", "Canyon funnel": "Canyon funnel", "Threads ahead": "Threads ahead", "Sprinkle": "Sprinkle", "Circuit board": "Circuit board", "Snow": "Snow", "Chart loading": "Chart loading", "Contact us": "Contact us", "Please use the form below to send us a message and we'll get back to you as soon as possible.": "Please use the form below to send us a message and we'll get back to you as soon as possible.", "Email": "Email", "Message": "Message", "Send": "Send", "Your message has been submitted.": "Your message has been submitted.", "Type to search...": "Type to search...", "Items per page": "Items per page", ":from - :to of :total": ":from - :to of :total", "Loading": "Loading", "[one 1 item|other :count items] selected": "[one 1 item|other :count items] selected", "There was an issue loading this page": "There was an issue loading this page", "Please try again later": "Please try again later", "Comment as :name": "Comment as :name", "Write a reply": "Write a reply", "Leave a comment": "Leave a comment", "Comment": "Comment", "d": "d", "hr": "hr", "min": "min", "sec": "sec", "per": "per", "a few seconds ago": "a few seconds ago", "Mark all as read": "Mark all as read", "Notifications": "Notifications", "Could not verify you are human.": "Could not verify you are human.", "Edit": "Edit", "Show less": "Show less", "Show more": "Show more", "Unfollow": "Unfollow", "Follow": "Follow", "No matching users": "No matching users", "Try another search query": "Try another search query", "Select a user": "Select a user", "Search for user by name or email": "Search for user by name or email", "Ads": "Ads", "Predefined Ad slots": "Predefined Ad slots", "Disable all add related functionality across the site.": "Disable all add related functionality across the site.", "Disable ads": "Disable ads", "Visitors report": "Visitors report", "Pageviews": "Pageviews", ":count total views": ":count total views", "Top devices": "Top devices", "Top browsers": "Top browsers", "Top locations": "Top locations", "Leave workspace": "Leave workspace", "Are you sure you want to leave this workspace?": "Are you sure you want to leave this workspace?", "All resources you've created in the workspace will be transferred to workspace owner.": "All resources you've created in the workspace will be transferred to workspace owner.", "Leave": "Leave", "Create workspace": "Create workspace", "Workspace name": "Workspace name", "Rename workspace": "Rename workspace", "Manage workspace members": "Manage workspace members", "Members of `:workspace`": "Members of `:workspace`", "And [one one other member|:count other members]": "And [one one other member|:count other members]", "Invite": "Invite", "Left workspace": "Left workspace", "Remove member": "Remove member", "Are you sure you want to remove `:name`?": "Are you sure you want to remove `:name`?", "All workspace resources created by `:name` will be transferred to workspace owner.": "All workspace resources created by `:name` will be transferred to workspace owner.", "You": "You", "Invited": "Invited", "Resend invite": "Resend invite", "Are you sure you want to send this invite again?": "Are you sure you want to send this invite again?", "Create new workspace": "Create new workspace", "worksapces": "worksapces", "Personal workspace": "Personal workspace", ":count members": ":count members", "Delete workspace": "Delete workspace", "Are you sure you want to delete “:name“?": "Are you sure you want to delete “:name“?", "Delete": "Delete", "Manage": "Manage", "Members": "Members", "Appearance": "Appearance", "Appearance editor": "Appearance editor", "Saved": "Saved", "Customizing": "Customizing", "Type of the file": "Type of the file", "Text": "Text", "Audio": "Audio", "Video": "Video", "PDF": "PDF", "Spreadsheet": "Spreadsheet", "Word Document": "Word Document", "Photoshop": "Photoshop", "Archive": "Archive", "Folder": "Folder", "Visibility": "Visibility", "Whether file is publicly accessible": "Whether file is publicly accessible", "Private": "Private", "Public": "Public", "Uploader": "Uploader", "User that this file was uploaded by": "User that this file was uploaded by", "File size": "File size", "Last updated": "Last updated", "Uploaded files and folders": "Uploaded files and folders", "Nothing has been uploaded yet": "Nothing has been uploaded yet", "No matching files or folders": "No matching files or folders", "Custom pages": "Custom pages", "No pages have been created yet": "No pages have been created yet", "No matching pages": "No matching pages", "New page": "New page", "Slug": "Slug", "Type of the page": "Type of the page", "User": "User", "User page was created by": "User page was created by", "Date page was created": "Date page was created", "Date page was last updated": "Date page was last updated", "Label": "Label", "No label...": "No label...", "Custom link": "Custom link", "Site page": "Site page", "Enter a url...": "Enter a url...", "Url": "Url", "Page": "Page", "Search pages": "Search pages", "Search...": "Search...", "Add role...": "Add role...", "Only show if user has role": "Only show if user has role", "Only show if user has permissions": "Only show if user has permissions", "Add permission...": "Add permission...", "Open link in": "Open link in", "Same window": "Same window", "New window": "New window", "Subscriptions": "Subscriptions", "Whether plan has any active subscriptions": "Whether plan has any active subscriptions", "Has active subscriptions": "Has active subscriptions", "Does not have active subscriptions": "Does not have active subscriptions", "Date plan was created": "Date plan was created", "Date plan was last updated": "Date plan was last updated", "Free": "Free", "Subscription plans": "Subscription plans", "No plans have been created yet": "No plans have been created yet", "No matching plans": "No matching plans", "Delete plan": "Delete plan", "Are you sure you want to delete this plan?": "Are you sure you want to delete this plan?", "Sync plans with Stripe & PayPal": "Sync plans with Stripe & PayPal", "Add new plan": "Add new plan", "Content type": "Content type", "Layout": "Layout", "Auto update": "Auto update", "Channels": "Channels", "No channels have been created yet": "No channels have been created yet", "No matching channels": "No matching channels", "Channels are used to display either all content of specific type or manually cured content. They can be shown as separate page or nested.": "Channels are used to display either all content of specific type or manually cured content. They can be shown as separate page or nested.", "Reset channels": "Reset channels", "Add new channel": "Add new channel", "Are you sure you want to reset channels to default ones? This will delete any manually created channels and any configuration changes made to them.": "Are you sure you want to reset channels to default ones? This will delete any manually created channels and any configuration changes made to them.", "Reset": "Reset", "Type of the role": "Type of the role", "Sitewide": "Sitewide", "Workspace": "Workspace", "Date role was created": "Date role was created", "Date role was last updated": "Date role was last updated", "Role": "Role", "Roles": "Roles", "No roles have been created yet": "No roles have been created yet", "No matching roles": "No matching roles", "Add new role": "Add new role", "Sitemap generated": "Sitemap generated", "Learn more": "Learn more", "Settings": "Settings", "General": "General", "Localization": "Localization", "Authentication": "Authentication", "Uploading": "Uploading", "Outgoing email": "Outgoing email", "Cache": "<PERSON><PERSON>", "Analytics": "Analytics", "Logging": "Logging", "Queue": "Queue", "Recaptcha": "<PERSON><PERSON><PERSON><PERSON>", "GDPR": "GDPR", "Menus": "Menus", "Seo": "<PERSON><PERSON>", "Themes": "Themes", "Update": "Update", "Create localization": "Create localization", "Language": "Language", "Search languages": "Search languages", "Localization created": "Localization created", "Language code": "Language code", "Translate": "Translate", "Localizations": "Localizations", "No localizations have been created yet": "No localizations have been created yet", "No matching localizations": "No matching localizations", "Add new localization": "Add new localization", "Add translation": "Add translation", "Add a new translation, if it does not exist already.": "Add a new translation, if it does not exist already.", "This should only need to be done for things like custom menu items.": "This should only need to be done for things like custom menu items.", "Translation key": "Translation key", "Translation value": "Translation value", "Add": "Add", ":locale translations": ":locale translations", "Add new": "Add new", "Save translations": "Save translations", "Update localization": "Update localization", "Localization updated": "Localization updated", "Suspend “:name“": "Suspend “:name“", "Suspend until": "Suspend until", "Permanent": "Permanent", "Reason": "Reason", "Optional": "Optional", "Suspend": "Suspend", "Profile image": "Profile image", "Add new user": "Add new user", "First name": "First name", "Last name": "Last name", "Whether email address has been confirmed. User will not be able to login until address is confirmed, unless confirmation is disabled from settings page.": "Whether email address has been confirmed. User will not be able to login until address is confirmed, unless confirmation is disabled from settings page.", "Email confirmed": "Email confirmed", "Allowed storage space": "Allowed storage space", "Total storage space all user uploads are allowed to take up. If left empty, this value will be inherited from any roles or subscriptions user has, or from 'Available space' setting in <a>Uploading</a> settings page.": "Total storage space all user uploads are allowed to take up. If left empty, this value will be inherited from any roles or subscriptions user has, or from 'Available space' setting in <a>Uploading</a> settings page.", "Permissions": "Permissions", "Resend email": "Resend email", "Edit “:email“": "Edit “:email“", "Suspended: :reason": "Suspended: :reason", "New password": "New password", "Subscribed": "Subscribed", "Created at": "Created at", "Edit user": "Edit user", "Suspend user": "Suspend user", "Remove suspension": "Remove suspension", "Are you sure you want to remove suspension from this user?": "Are you sure you want to remove suspension from this user?", "Unsuspend": "Unsuspend", "Login as user": "Login as user", "Login as “:name“": "Login as “:name“", "Are you sure you want to login as this user?": "Are you sure you want to login as this user?", "Login": "<PERSON><PERSON>", "Email verification status": "Email verification status", "is confirmed": "is confirmed", "is not confirmed": "is not confirmed", "Date user registered or was created": "Date user registered or was created", "Date user was last updated": "Date user was last updated", "Subscription": "Subscription", "Whether user is subscribed or not": "Whether user is subscribed or not", "is subscribed": "is subscribed", "is not subscribed": "is not subscribed", "Users": "Users", "No users have been created yet": "No users have been created yet", "Add new tag": "Add new tag", "Unique tag identifier.": "Unique tag identifier.", "Display name": "Display name", "User friendly tag name.": "User friendly tag name.", "Type of the tag": "Type of the tag", "Date tag was created": "Date tag was created", "Date tag was last updated": "Date tag was last updated", "No tags have been created yet": "No tags have been created yet", "No matching tags": "No matching tags", "Update “:name“ tag": "Update “:name“ tag", "Add new subscription": "Add new subscription", "Plan": "Plan", "Price": "Price", "Renews at": "Renews at", "This will only change local records. User will continue to be billed on their original cycle on the payment gateway.": "This will only change local records. User will continue to be billed on their original cycle on the payment gateway.", "Ends at": "Ends at", "Status": "Status", "Whether subscription is active or cancelled": "Whether subscription is active or cancelled", "Active": "Active", "Cancelled": "Cancelled", "Stripe": "Stripe", "PayPal": "PayPal", "None": "None", "Gateway": "Gateway", "With which payment provider was subscription created": "With which payment provider was subscription created", "Renew date": "Renew date", "Date subscription will renew": "Date subscription will renew", "Date subscription was created": "Date subscription was created", "Date subscription was last updated": "Date subscription was last updated", "Customer": "Customer", "No subscriptions have been created yet": "No subscriptions have been created yet", "No matching subscriptions": "No matching subscriptions", "Cancel subscription": "Cancel subscription", "Are you sure you want to cancel this subscription?": "Are you sure you want to cancel this subscription?", "This will put user on grace period until their next scheduled renewal date. Subscription can be renewed until that date by user or from admin area.": "This will put user on grace period until their next scheduled renewal date. Subscription can be renewed until that date by user or from admin area.", "Confirm": "Confirm", "Renew subscription": "Renew subscription", "Resume subscription": "Resume subscription", "Are you sure you want to resume this subscription?": "Are you sure you want to resume this subscription?", "This will put user on their original plan and billing cycle.": "This will put user on their original plan and billing cycle.", "Delete subscription": "Delete subscription", "Are you sure you want to delete this subscription?": "Are you sure you want to delete this subscription?", "This will permanently delete the subscription and immediately cancel it on billing gateway. Subscription will not be renewable anymore.": "This will permanently delete the subscription and immediately cancel it on billing gateway. Subscription will not be renewable anymore.", "Update subscription": "Update subscription", "Billing": "Billing", "Plans": "Plans", "Change your plan": "Change your plan", "Available plans": "Available plans", "What's included": "What's included", "Current plan": "Current plan", "Continue": "Continue", "Cancel your plan": "Cancel your plan", "Your plan will be canceled, but is still available until the end of your billing period on :date": "Your plan will be canceled, but is still available until the end of your billing period on :date", "If you change your mind, you can renew your subscription.": "If you change your mind, you can renew your subscription.", "Cancel plan": "Cancel plan", "Go back": "Go back", "By cancelling your plan, you agree to our terms of service and privacy policy.": "By cancelling your plan, you agree to our terms of service and privacy policy.", "Confirm your new plan": "Confirm your new plan", "Changing to": "Changing to", "You will be charged the new price starting :date": "You will be charged the new price starting :date", "By confirming your new plan, you agree to our terms of Service and privacy policy.": "By confirming your new plan, you agree to our terms of Service and privacy policy.", "Renew": "<PERSON>w", "Renew your plan": "Renew your plan", "This plan will no longer be canceled. It will renew on :date": "This plan will no longer be canceled. It will renew on :date", "Renew plan": "Renew plan", "By renewing your plan, you agree to our terms of service and privacy policy.": "By renewing your plan, you agree to our terms of service and privacy policy.", "Your password has been reset!": "Your password has been reset!", "Email sent": "Email sent", "An error occurred. Please try again later": "An error occurred. Please try again later", "Don't have an account? <a>Sign up.</a>": "Don't have an account? <a>Sign up.</a>", "Forgot Password": "Forgot Password", "Enter your email address below and we will send you a link to reset or create your password.": "Enter your email address below and we will send you a link to reset or create your password.", "To join your team on :siteName, login to your account": "To join your team on :siteName, login to your account", "Sign in to your account": "Sign in to your account", "Forgot your password?": "Forgot your password?", "Stay signed in for a month": "Stay signed in for a month", "Or sign in with": "Or sign in with", "OR": "OR", "Show advanced permissions": "Show advanced permissions", "Create a new account": "Create a new account", "To join your team on :siteName, create an account": "To join your team on :siteName, create an account", "First, let's create your account": "First, let's create your account", "Already have an account? <a>Sign in.</a>": "Already have an account? <a>Sign in.</a>", "Register": "Register", "Confirm password": "Confirm password", "Create account": "Create account", "Or sign up with": "Or sign up with", "I accept the :name": "I accept the :name", "Reset your account password": "Reset your account password", "Reset Password": "Reset Password", "Reset password": "Reset password", "Continue with google": "Continue with google", "Continue with facebook": "Continue with facebook", "Continue with twitter": "Continue with twitter", "Continue with envato": "Continue with envato", "Password required": "Password required", "An account with this email address already exists. If you want to connect the two accounts, enter existing account password.": "An account with this email address already exists. If you want to connect the two accounts, enter existing account password.", "Connect": "Connect", "Checkout": "Checkout", "Summary": "Summary", "Billed today": "Billed today", "You’ll be charged until you cancel your subscription. Previous charges won’t be refunded when you cancel unless it’s legally required. Your payment data is encrypted and secure. By subscribing your agree to our terms of service and privacy policy.": "You’ll be charged until you cancel your subscription. Previous charges won’t be refunded when you cancel unless it’s legally required. Your payment data is encrypted and secure. By subscribing your agree to our terms of service and privacy policy.", "or": "or", "Annual": "Annual", "Monthly": "Monthly", "Monthly billing": "Monthly billing", "Yearly billing": "Yearly billing", "Pricing": "Pricing", "Choose the right plan for you": "Choose the right plan for you", "Do you have any questions about PRO accounts?": "Do you have any questions about PRO accounts?", "Our support team will be happy to assist you.": "Our support team will be happy to assist you.", "Most popular": "Most popular", "Get started": "Get started", "Save up to :percentage%": "Save up to :percentage%", "Don't repeat": "Don't repeat", "Horizontal": "Horizontal", "Vertical": "Vertical", "Both": "Both", "Auto": "Auto", "Stretch to fit": "Stretch to fit", "Fit image": "Fit image", "Fixed": "Fixed", "Not fixed": "Not fixed", "Repeat": "Repeat", "Position": "Position", "Upload image": "Upload image", "Select": "Select", "Repeat image": "Repeat image", "Default": "<PERSON><PERSON><PERSON>", "Login page": "Login page", "Registration page": "Registration page", "Csv export": "Csv export", "Your request is being processed. We'll email you when the report is ready to download. In\n            certain cases, it might take a little longer, depending on the number of items beings\n            exported and the volume of activity.": "Your request is being processed. We'll email you when the report is ready to download. In\n            certain cases, it might take a little longer, depending on the number of items beings\n            exported and the volume of activity.", "Got it": "Got it", "Remove domain?": "Remove domain?", "Are you sure you want to remove “:domain“?": "Are you sure you want to remove “:domain“?", "Domain": "Domain", "Global": "Global", "Whether domain is marked as global": "Whether domain is marked as global", "Date domain was created": "Date domain was created", "Date domain was last updated": "Date domain was last updated", "User domain belongs to": "User domain belongs to", "No domains have been connected yet": "No domains have been connected yet", "No matching domains": "No matching domains", "Try another search query or different filters": "Try another search query or different filters", "Delete [one 1 item|other :count items]?": "Delete [one 1 item|other :count items]?", "This will permanently remove the items and cannot be undone.": "This will permanently remove the items and cannot be undone.", "Deleted [one 1 record|other :count records]": "Deleted [one 1 record|other :count records]", "Could not delete records": "Could not delete records", "Schedule": "Schedule", "Error": "Error", "Filter": "Filter", "Clear": "Clear", "is": "is", "is not": "is not", "is greater than": "is greater than", "is greater than or equal to": "is greater than or equal to", "is less than": "is less than", "is less than or equal to": "is less than or equal to", "Include": "Include", "Do not include": "Do not include", "Is between": "Is between", "Include all": "Include all", "Date created": "Date created", "Upgrade to unlock this feature and many more.": "Upgrade to unlock this feature and many more.", "You don't have permissions to access this feature.": "You don't have permissions to access this feature.", "You've reached the maximum number of :resource allowed for your current plan.": "You've reached the maximum number of :resource allowed for your current plan.", "Upgrade to increase this limit and unlock other features.": "Upgrade to increase this limit and unlock other features.", "You can't create new :name in this workspace.": "You can't create new :name in this workspace.", "Your plan is at its maximum number of :name allowed. <a>Upgrade to add more.</a>": "Your plan is at its maximum number of :name allowed. <a>Upgrade to add more.</a>", "To unlock ability to create :name. <a>Upgrade your plan.</a>": "To unlock ability to create :name. <a>Upgrade your plan.</a>", "You don't have permissions to create :name.": "You don't have permissions to create :name.", "Join the PROs": "Join the PROs", "Maybe later": "Maybe later", "Find out more": "Find out more", "Account required": "Account required", "[COMMENT DELETED]": "[COMMENT DELETED]", "Reply": "Reply", "at :position": "at :position", "More": "More", "Report comment": "Report comment", "Delete comment?": "Delete comment?", "Are you sure you want to delete this comment?": "Are you sure you want to delete this comment?", "Please <l>login</l> or <r>create account</r> to comment": "Please <l>login</l> or <r>create account</r> to comment", "Loading comments...": "Loading comments...", ":count comments": ":count comments", "Seems a little quiet over here": "Seems a little quiet over here", "Be the first to comment": "Be the first to comment", "Reported [one 1 time|other :count times]": "Reported [one 1 time|other :count times]", "Save edit": "Save edit", "Whether comment is active or deleted": "Whether comment is active or deleted", "Deleted": "Deleted", "Reported": "Reported", "Show only reported comments": "Show only reported comments", "User comment was created by": "User comment was created by", "Date comment was created": "Date comment was created", "Date comment was last updated": "Date comment was last updated", "Comments": "Comments", "No comments have been created yet": "No comments have been created yet", "No matching comments": "No matching comments", "Delete [one comment|other :count comments]": "Delete [one comment|other :count comments]", "Are you sure you want to delete selected comments?": "Are you sure you want to delete selected comments?", "Comment posted": "Comment posted", "[one Comment deleted|other Deleted :count comments]": "[one Comment deleted|other Deleted :count comments]", "Restored [one 1 comment|other :count comments]": "Restored [one 1 comment|other :count comments]", "Comment updated": "Comment updated", "Hang tight!": "Hang tight!", "Notifications will start showing up here soon.": "Notifications will start showing up here soon.", "Notification settings": "Notification settings", "Update preferences": "Update preferences", "Notifications blocked. Please enable them for this site from browser settings.": "Notifications blocked. Please enable them for this site from browser settings.", "Align left": "<PERSON><PERSON> left", "Align center": "Align center", "Align right": "Align right", "Justify": "Justify", "Clear formatting": "Clear formatting", "Codeblock": "Codeblock", "Bold": "Bold", "Italic": "Italic", "Underline": "Underline", "Format": "Format", "Heading :number": "Heading :number", "Code": "Code", "Strikethrough": "Strikethrough", "Superscript": "Superscript", "Subscript": "Subscript", "Blockquote": "Blockquote", "Paragraph": "Paragraph", "Insert image": "Insert image", "Decrease indent": "Decrease indent", "Increase indent": "Increase indent", "Horizontal rule": "Horizontal rule", "Embed": "Embed", "Important": "Important", "Warning": "Warning", "Note": "Note", "Insert link": "Insert link", "Embed URL": "Embed URL", "URL": "URL", "Text to display": "Text to display", "Open link in...": "Open link in...", "Current window": "Current window", "Bulleted list": "Bulleted list", "Numbered list": "Numbered list", "Source": "Source", "Source code": "Source code", "Breadcrumbs": "Breadcrumbs", "We use cookies to optimize site functionality and provide you with the\n      best possible experience.": "We use cookies to optimize site functionality and provide you with the\n      best possible experience.", "OK": "OK", "White": "White", "Solitude": "Solitude", "Wheat": "Wheat", "Cape Honey": "Cape Honey", "Milk punch": "Milk punch", "Dingy": "Dingy", "Aquamarine": "Aquamarine", "Cosmic Latte": "Cosmic Latte", "Geraldine": "<PERSON><PERSON>", "Sundown": "Sundown", "Pelorous": "Pelorous", "Deep Lilac": "Deep Lilac", "Blue marguerite": "Blue marguerite", "Americano": "Americano", "Black": "Black", "Blue zodiac": "Blue zodiac", "Comet": "Comet", "System": "System", "Search fonts": "Search fonts", "All categories": "All categories", "Serif": "<PERSON><PERSON>", "Sans serif": "Sans serif", "Display": "Display", "Handwriting": "Handwriting", "Monospace": "Monospace", "No matching fonts": "No matching fonts", "Try another search query or different category": "Try another search query or different category", "Copyright © :year :name, All Rights Reserved": "Copyright © :year :name, All Rights Reserved", "Light mode": "Light mode", "Dark mode": "Dark mode", "Select item...": "Select item...", "Find an item...": "Find an item...", "Change item": "Change item", "Load all": "Load all", "Select icon": "Select icon", "Search icons...": "Search icons...", "+ :count more": "+ :count more", "Remove image": "Remove image", "Use default": "Use default", "Replace": "Replace", "Replace image": "Replace image", "Please select an image.": "Please select an image.", "Looks like you've found the doorway to the great nothing": "Looks like you've found the doorway to the great nothing", "Sorry about that! Please visit our homepage to get where you need\n                to go.": "Sorry about that! Please visit our homepage to get where you need\n                to go.", "Take me there!": "Take me there!", "Select all": "Select all", "Report removed": "Report removed", "Thanks for reporting. We will review this content.": "Thanks for reporting. We will review this content.", "There was an issue. Please try again.": "There was an issue. Please try again.", ":fileName thumbnail": ":fileName thumbnail", "Following :name": "Following :name", "Stopped following :name": "Stopped following :name", "Top Locations": "Top Locations", "Back to countries": "Back to countries", "Zooming in": "Zooming in", "Click on a country inside the map or country list to zoom in and see city data for that country.": "Click on a country inside the map or country list to zoom in and see city data for that country.", "City": "City", "Country": "Country", "Clicks": "<PERSON>licks", "Upload timed out": "Upload timed out", "`:file` exceeds maximum allowed size of :size": "`:file` exceeds maximum allowed size of :size", "This file type is not allowed": "This file type is not allowed", "Role changed": "Role changed", "Created workspace": "Created workspace", "Declined workspace invitation": "Declined workspace invitation", "This invite is no longer valid": "This invite is no longer valid", "Deleted workspace": "Deleted workspace", "Joined workspace": "Joined workspace", "Updated workspace": "Updated workspace", "Font": "Font", "Rounding": "Rounding", "Custom Code": "Custom Code", "Custom code": "Custom code", "SEO Settings": "SEO Settings", "SEO": "SEO", "Changes saved": "Changes saved", "Favicon": "Favicon", "This will generate different size favicons. Image should be at least 512x512 in size.": "This will generate different size favicons. Image should be at least 512x512 in size.", "Light logo": "Light logo", "Will be used on dark backgrounds.": "Will be used on dark backgrounds.", "Dark logo": "Dark logo", "Will be used on light backgrounds. Will default to light logo if left empty.": "Will be used on light backgrounds. Will default to light logo if left empty.", "Mobile light logo": "Mobile light logo", "Will be used on light backgrounds on mobile. Will default to desktop logo if left empty.": "Will be used on light backgrounds on mobile. Will default to desktop logo if left empty.", "Mobile dark logo": "Mobile dark logo", "Will be used on dark backgrounds on mobile. Will default to desktop if left empty.": "Will be used on dark backgrounds on mobile. Will default to desktop if left empty.", "Site name": "Site name", "Site description": "Site description", "Daily": "Daily", "Weekly": "Weekly", "Every 3 months": "Every 3 months", "Every 6 months": "Every 6 months", "Yearly": "Yearly", "Custom": "Custom", "Create new plan": "Create new plan", "Position in pricing table": "Position in pricing table", "First": "First", "Second": "Second", "Third": "Third", "Fourth": "Fourth", "Fifth": "Fifth", "Total storage space all user uploads are allowed to take up.": "Total storage space all user uploads are allowed to take up.", "Plan will be displayed more prominently on pricing page.": "Plan will be displayed more prominently on pricing page.", "Recommend": "Recommend", "Plan will not be shown on pricing or upgrade pages.": "Plan will not be shown on pricing or upgrade pages.", "Hidden": "Hidden", "Will be assigned to all users, if they are not subscribed already.": "Will be assigned to all users, if they are not subscribed already.", "Feature list": "Feature list", "Add another line": "Add another line", "Add another price": "Add another price", "Edit “:name“ plan": "Edit “:name“ plan", "This price can't modified or deleted, because it has [one 1 subscriber|other :count subscribers]. You can instead add a new price.": "This price can't modified or deleted, because it has [one 1 subscriber|other :count subscribers]. You can instead add a new price.", "Amount": "Amount", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Search currencies": "Search currencies", "Delete price": "Delete price", "Billing period": "Billing period", "Every": "Every", "Days": "Days", "Weeks": "Weeks", "Months": "Months", "Plan created": "Plan created", "Plan deleted": "Plan deleted", "Plans synced": "Plans synced", "Could not sync plans": "Could not sync plans", "Plan updated": "Plan updated", "Drag handle": "Drag handle", "Content item": "Content item", "Channel content": "Channel content", "Channel is empty": "Channel is empty", "No content is attached to this channel yet.": "No content is attached to this channel yet.", "This channel is listing all available content of specified type, and can't be curated manually.": "This channel is listing all available content of specified type, and can't be curated manually.", "This channel content is set to update automatically and can't be curated manually.": "This channel content is set to update automatically and can't be curated manually.", "Update content now": "Update content now", "Search for content to add...": "Search for content to add...", "Details": "Details", "SEO title": "SEO title", "SEO description": "SEO description", "Edit “:name“ channel": "Edit “:name“ channel", "Role description...": "Role description...", "Whether this role will be assigned to users globally on the site or only within workspaces.": "Whether this role will be assigned to users globally on the site or only within workspaces.", "Assign this role to new users automatically.": "Assign this role to new users automatically.", "Assign this role to guests (not logged in users).": "Assign this role to guests (not logged in users).", "Guests": "Guests", "Assigned at": "Assigned at", "Users can't be assigned to this role": "Users can't be assigned to this role", "No users have been assigned to this role yet": "No users have been assigned to this role yet", "Assign user": "Assign user", "Remove users": "Remove users", "Remove [one 1 user|other :count users] from “:name“ role?": "Remove [one 1 user|other :count users] from “:name“ role?", "This will permanently remove the users.": "This will permanently remove the users.", "Edit “:name“ role": "Edit “:name“ role", "Page created": "Page created", "Page updated": "Page updated", "Assigned [one 1 user|other :count users] to {role}": "Assigned [one 1 user|other :count users] to {role}", "Removed [one 1 user|other :count users] from “{role}“": "Removed [one 1 user|other :count users] from “{role}“", "Role updated": "Role updated", "Created new role": "Created new role", "Settings updated": "Settings updated", "User created": "User created", "User updated": "User updated", "User suspended": "User suspended", "User unsuspended": "User unsuspended", "Tag created": "Tag created", "Tag updated": "Tag updated", "Channel created": "Channel created", "Channels reset to default": "Channels reset to default", "Channel content updated": "Channel content updated", "Channel updated": "Channel updated", "Subscription created": "Subscription created", "Subscription updated": "Subscription updated", "Payment method changed successfully!": "Payment method changed successfully!", "Your request is processing. We'll update you when your payment method is confirmed.": "Your request is processing. We'll update you when your payment method is confirmed.", "Payment method confirmation failed. Please try another payment method.": "Payment method confirmation failed. Please try another payment method.", "Something went wrong": "Something went wrong", "Payment method": "Payment method", "Change payment method": "Change payment method", "Change": "Change", "Your plan renews on :date": "Your plan renews on :date", "Change plan": "Change plan", "Canceled": "Canceled", "Your plan will be canceled on :date": "Your plan will be canceled on :date", "Payment history": "Payment history", "No invoices yet": "No invoices yet", "Paid": "Paid", "Unpaid": "Unpaid", "Expires :date": "Expires :date", "Configure registration, social login and related 3rd party integrations.": "Configure registration, social login and related 3rd party integrations.", "All registration related functionality (including social login) will be disabled.": "All registration related functionality (including social login) will be disabled.", "Disable registration": "Disable registration", "Only allow one device to be logged into user account at the same time.": "Only allow one device to be logged into user account at the same time.", "Single device login": "Single device login", "Use compact design for social login buttons.": "Use compact design for social login buttons.", "Compact buttons": "Compact buttons", "Domain blacklist": "Domain blacklist", "Comma separated list of domains. Users will not be able to register or login using any email adress from specified domains.": "Comma separated list of domains. Users will not be able to register or login using any email adress from specified domains.", "Outgoing mail method needs to be setup before enabling this setting. <a>Fix now</a>": "Outgoing mail method needs to be setup before enabling this setting. <a>Fix now</a>", "Require newly registered users to validate their email address before being able to login.": "Require newly registered users to validate their email address before being able to login.", "Require email confirmation": "Require email confirmation", "Enable logging into the site via envato.": "Enable logging into the site via envato.", "Envato login": "<PERSON><PERSON><PERSON> login", "Envato ID": "Envato ID", "Envato secret": "Envato secret", "Envato personal token": "<PERSON><PERSON><PERSON> personal token", "Enable logging into the site via google.": "Enable logging into the site via google.", "Google login": "Google login", "Google client ID": "Google client ID", "Google client secret": "Google client secret", "Enable logging into the site via facebook.": "Enable logging into the site via facebook.", "Facebook login": "Facebook login", "Facebook app ID": "Facebook app ID", "Facebook app secret": "Facebook app secret", "Enable logging into the site via twitter.": "Enable logging into the site via twitter.", "Twitter login": "Twitter login", "Twitter ID": "Twitter ID", "Twitter secret": "Twitter secret", "Configure settings related to EU General Data Protection Regulation.": "Configure settings related to EU General Data Protection Regulation.", "Whether cookie notice should be shown automatically to users from EU until it is accepted.": "Whether cookie notice should be shown automatically to users from EU until it is accepted.", "Enable cookie notice": "Enable cookie notice", "Information button": "Information button", "Cookie notice position": "<PERSON>ie notice position", "Top": "Top", "Bottom": "Bottom", "Registration policies": "Registration policies", "Create policies that will be shown on registration page. User will be required to accept them by toggling a checkbox.": "Create policies that will be shown on registration page. User will be required to accept them by toggling a checkbox.", "Add another policy": "Add another policy", "Add policy": "Add policy", "Configure site url, homepage, theme and other general settings.": "Configure site url, homepage, theme and other general settings.", "Base site url is set as <b>:baseUrl</b> in configuration, but current url is <b>:currentUrl</b>. It is recommended to set the primary url you want to use in configuration file and then redirect all other url versions to this primary version via cpanel or .htaccess file.": "Base site url is set as <b>:baseUrl</b> in configuration, but current url is <b>:currentUrl</b>. It is recommended to set the primary url you want to use in configuration file and then redirect all other url versions to this primary version via cpanel or .htaccess file.", "Primary site url": "Primary site url", "Site home page": "Site home page", "Which page should be used as site homepage.": "Which page should be used as site homepage.", "Homepage :name": "Homepage :name", "Default site theme": "Default site theme", "Which theme to use for users that have not chosen a theme manually.": "Which theme to use for users that have not chosen a theme manually.", "Allow users to manually change site theme.": "Allow users to manually change site theme.", "Allow theme change": "Allow theme change", "Generate sitemap": "Generate sitemap", "Once generated, sitemap url will be: :url": "Once generated, sitemap url will be: :url", "Configure global date, time and language settings.": "Configure global date, time and language settings.", "Default timezone": "Default timezone", "Search timezones": "Search timezones", "Which timezone should be selected by default for new users and guests.": "Which timezone should be selected by default for new users and guests.", "Default language": "Default language", "Which localization should be selected by default for new users and guests.": "Which localization should be selected by default for new users and guests.", "Date verbosity": "Date verbosity", "Default verbosity for all dates displayed across the site. Month/day order and separators will be adjusted automatically, based on user's locale.": "Default verbosity for all dates displayed across the site. Month/day order and separators will be adjusted automatically, based on user's locale.", "If disabled, site will always be shown in default language and user will not be able to change their locale.": "If disabled, site will always be shown in default language and user will not be able to change their locale.", "Enable translations": "Enable translations", "Error logging": "Error logging", "Configure site error logging and related 3rd party integrations.": "Configure site error logging and related 3rd party integrations.", "<a>Sentry</a> integration provides real-time error tracking and helps identify and fix issues when site is in production.": "<a>Sentry</a> integration provides real-time error tracking and helps identify and fix issues when site is in production.", "Sentry DSN": "Sentry DSN", "Select active queue method and enter related 3rd party API keys.": "Select active queue method and enter related 3rd party API keys.", "Queues allow to defer time consuming tasks, such as sending an email, until a later time. Deferring these tasks can speed up web requests to the application.": "Queues allow to defer time consuming tasks, such as sending an email, until a later time. Deferring these tasks can speed up web requests to the application.", "All methods except sync require additional setup, which should be performed before changing the queue method. Consult documentation for more information.": "All methods except sync require additional setup, which should be performed before changing the queue method. Consult documentation for more information.", "Queue method": "Queue method", "Sync (Default)": "Sync (<PERSON><PERSON><PERSON>)", "Database": "Database", "SQS (Amazon simple queue service)": "SQS (Amazon simple queue service)", "SQS queue key": "SQS queue key", "SQS queue secret": "SQS queue secret", "SQS queue prefix": "SQS queue prefix", "SQS queue name": "SQS queue name", "SQS queue region": "SQS queue region", "Configure google recaptcha integration and credentials.": "Configure google recaptcha integration and credentials.", "Enable recaptcha integration when creating links from homepage or user dashboard.": "Enable recaptcha integration when creating links from homepage or user dashboard.", "Link creation": "Link creation", "Enable recaptcha integration for \"contact us\" page.": "Enable recaptcha integration for \"contact us\" page.", "Contact page": "Contact page", "Enable recaptcha integration for registration page.": "Enable recaptcha integration for registration page.", "Recaptcha v3 site key": "Recaptcha v3 site key", "Recaptcha v3 secret key": "Recaptcha v3 secret key", "Configure google analytics integration and credentials.": "Configure google analytics integration and credentials.", "Google service account key file (.json)": "Google service account key file (.json)", "Google analytics property ID": "Google analytics property ID", "Google analytics measurement ID only, not the whole javascript snippet.": "Google analytics measurement ID only, not the whole javascript snippet.", "Google tag manager measurement ID": "Google tag manager measurement ID", "Google maps javascript API key": "Google maps javascript API key", "Only required in order to show world geochart on integrated analytics pages.": "Only required in order to show world geochart on integrated analytics pages.", "Configure gateway integration, accepted cards, invoices and other related settings.": "Configure gateway integration, accepted cards, invoices and other related settings.", "Invoices": "Invoices", "Enable or disable all subscription related functionality across the site.": "Enable or disable all subscription related functionality across the site.", "Enable subscriptions": "Enable subscriptions", "Accepted cards": "Accepted cards", "Add new card...": "Add new card...", "Invoice address": "Invoice address", "Invoice notes": "Invoice notes", "Default notes to show under `notes` section of user invoice. Optional.": "Default notes to show under `notes` section of user invoice. Optional.", "Enable PayPal payment gateway integration.": "Enable PayPal payment gateway integration.", "PayPal gateway": "PayPal gateway", "PayPal Client ID": "PayPal Client ID", "PayPal Secret": "PayPal Secret", "PayPal Webhook ID": "PayPal Webhook ID", "Allows testing PayPal payments with sandbox accounts.": "Allows testing PayPal payments with sandbox accounts.", "PayPal test mode": "PayPal test mode", "Enable Stripe payment gateway integration.": "Enable Stripe payment gateway integration.", "Stripe gateway": "Stripe gateway", "Stripe publishable key": "Stripe publishable key", "Stripe secret key": "Stripe secret key", "Stripe webhook signing secret": "Stripe webhook signing secret", "Subscription deleted.": "Subscription deleted.", "Subscription cancelled.": "Subscription cancelled.", "Plan changed.": "Plan changed.", "Subscription renewed.": "Subscription renewed.", "Account Settings": "Account <PERSON><PERSON>", "Account settings": "Account settings", "View and update your account details, profile and more.": "View and update your account details, profile and more.", "Two factor authentication": "Two factor authentication", "Account details": "Account details", "Social login": "Social login", "Active sessions": "Active sessions", "Location and language": "Location and language", "Developers": "Developers", "Delete account": "Delete account", "Date, time and language": "Date, time and language", "Search countries": "Search countries", "Timezone": "Timezone", "Manage social login": "Manage social login", "If you disable social logins, you'll still be able to log in using your email and password.": "If you disable social logins, you'll still be able to log in using your email and password.", ":service account": ":service account", "Disabled": "Disabled", "Disabled :service account": "Disabled :service account", "Enabled :service account": "Enabled :service account", "Disable": "Disable", "Enable": "Enable", "Go to homepage": "Go to homepage", "For your security, please confirm your password to continue.": "For your security, please confirm your password to continue.", "Logout": "Logout", "Verify your email": "Verify your email", "Enter the verification code we sent to :email": "Enter the verification code we sent to :email", "Enter your verification code": "Enter your verification code", "Next": "Next", "If you don't see the email in your inbox, check your spam folder and promotions tab. If you still don't see it, <a>request a resend</a>.": "If you don't see the email in your inbox, check your spam folder and promotions tab. If you still don't see it, <a>request a resend</a>.", "Confirm access to your account by entering the authentication code provided by your authenticator application.": "Confirm access to your account by entering the authentication code provided by your authenticator application.", "Recovery code": "Recovery code", "Use recovery code instead": "Use recovery code instead", "Subscription successful!": "Subscription successful!", "Return to site": "Return to site", "Something went wrong. Please try again.": "Something went wrong. Please try again.", "Payment processing. We'll update you when payment is received.": "Payment processing. We'll update you when payment is received.", "Payment failed. Please try another payment method.": "Payment failed. Please try another payment method.", "Connect domain": "Connect domain", "Previous": "Previous", "Checking DNS configuration...": "Checking DNS configuration...", "Connecting domain...": "Connecting domain...", "Don't close this window until domain is connected.": "Don't close this window until domain is connected.", "Host": "Host", "Enter the exact domain name you want your items to be accessible with. It can be a subdomain (example.yourdomain.com) or root domain (yourdomain.com).": "Enter the exact domain name you want your items to be accessible with. It can be a subdomain (example.yourdomain.com) or root domain (yourdomain.com).", "Whether all users should be able to select this domain.": "Whether all users should be able to select this domain.", "Add this CNAME record to your domain by visiting your DNS provider or registrar.": "Add this CNAME record to your domain by visiting your DNS provider or registrar.", "Add this A record to your domain by visiting your DNS provider or registrar.": "Add this A record to your domain by visiting your DNS provider or registrar.", "DNS records for the domain are setup, however it seems that your server is not configured to handle requests from “:host“": "DNS records for the domain are setup, however it seems that your server is not configured to handle requests from “:host“", "The domain is missing :record record pointing to :location or the changes haven't propagated yet.": "The domain is missing :record record pointing to :location or the changes haven't propagated yet.", "You can wait and try again later, or <b>refresh</b>": "You can wait and try again later, or <b>refresh</b>", "“:domain” connected": "“:domain” connected", "“:domain” removed": "“:domain” removed", "Date": "Date", "Severity": "Severity", "Error log": "Error log", "No errors have been logged yet": "No errors have been logged yet", "No matching error log entries": "No matching error log entries", "Download log": "Download log", "Delete log file": "Delete log file", "Are you sure you want to delete this log file?": "Are you sure you want to delete this log file?", "Error details": "Error details", "Log file deleted": "Log file deleted", ":names + :count more": ":names + :count more", "Ran at": "Ran at", "Duration": "Duration", "Completed": "Completed", "Runs recently": "Runs recently", "Rerun now": "<PERSON><PERSON> now", "CRON schedule log": "CRON schedule log", "No scheduled commands have ran yet": "No scheduled commands have ran yet", "No matching scheduled commands": "No matching scheduled commands", "Command reran": "Command reran", "Subject": "Subject", "Sent": "<PERSON><PERSON>", "Not sent": "Not sent", "From": "From", "To": "To", "Status of the outgoing email": "Status of the outgoing email", "Date email send was attempted": "Date email send was attempted", "No outgoing emails have been logged yet": "No outgoing emails have been logged yet", "No matching emails": "No matching emails", "Email preview": "Email preview", "Updated preferences": "Updated preferences", "Exit fullscreen (f)": "Exit fullscreen (f)", "Enter fullscreen (f)": "Enter fullscreen (f)", "Exit picture-in-picture (p)": "Exit picture-in-picture (p)", "Enter picture-in-picture (p)": "Enter picture-in-picture (p)", "Pause (k)": "Pause (k)", "Play (k)": "Play (k)", "Speed": "Speed", "Quality": "Quality", "Subtitles/CC": "Subtitles/CC", "Playback speed": "Playback speed", "Playback quality": "Playback quality", "Subtitles/Captions": "Subtitles/Captions", "Off": "Off", "Enable repeat one": "Enable repeat one", "Disable repeat": "Disable repeat", "Enable repeat": "Enable repeat", "Disable shuffle": "Disable shuffle", "Enable shuffle": "Enable shuffle", "Hide subtitles/captions (c)": "Hide subtitles/captions (c)", "Show subtitles/captions (c)": "Show subtitles/captions (c)", "Unmute": "Unmute", "Mute": "Mute", "Remove file": "Remove file", "Replace file": "Replace file", "Please select a file.": "Please select a file.", "Select an option...": "Select an option...", "minimum": "minimum", "maximum": "maximum", "There are no items matching your query": "There are no items matching your query", "Site logo": "Site logo", "Profile page": "Profile page", "Log out": "Log out", "Dismiss": "<PERSON><PERSON><PERSON>", "No file preview available": "No file preview available", "Preview for :name": "Preview for :name", "Loading file contents": "Loading file contents", "This file is too large to preview.": "This file is too large to preview.", "There was an issue previewing this file": "There was an issue previewing this file", "Custom HTML & JavaScript": "Custom HTML & JavaScript", "Custom CSS": "Custom CSS", "Add menu item": "Add menu item", "Link": "Link", "Route": "Route", "Add to menu": "Add to menu", "Menu name": "Menu name", "Menu positions": "Menu positions", "Where should this menu appear on the site": "Where should this menu appear on the site", "Menu items": "Menu items", "No menu items yet": "No menu items yet", "Click “add“ button to start adding links, pages, routes and other items to this menu. ": "Click “add“ button to start adding links, pages, routes and other items to this menu. ", "Delete menu": "Delete menu", "Delete this item": "Delete this item", "Delete menu item": "Delete menu item", "New menu :number": "New menu :number", "Create menu": "Create menu", "Accent": "Accent", "Background": "Background", "Background alt": "Background alt", "Transparent": "Transparent", "Navbar": "<PERSON><PERSON><PERSON>", "Foreground": "Foreground", "Accent light": "Accent light", "Accent dark": "Accent dark", "Text on accent": "Text on accent", "Chip": "Chip", "New theme": "New theme", "At least one theme is required": "At least one theme is required", "Reset colors": "Reset colors", "Delete theme": "Delete theme", "Are you sure you want to delete this theme?": "Are you sure you want to delete this theme?", "Square": "Square", "Small": "Small", "Medium": "Medium", "Large": "Large", "Larger": "Larger", "Pill": "<PERSON>ll", "Button rounding": "<PERSON>ton rounding", "Input rounding": "Input rounding", "Panel rounding": "Panel rounding", "Update settings": "Update settings", "Whether this theme has light text on dark background.": "Whether this theme has light text on dark background.", "Dark theme": "Dark theme", "When light mode is selected, this theme will be used.": "When light mode is selected, this theme will be used.", "Default for light mode": "Default for light mode", "When dark mode is selected, this theme will be used.": "When dark mode is selected, this theme will be used.", "Default for dark mode": "Default for dark mode", ":restriction_name": ":restriction_name", "Auto update method": "Auto update method", "This option will automatically update channel content every 24 hours using the specified method.": "This option will automatically update channel content every 24 hours using the specified method.", "Layout when nested": "Layout when nested", "Type of content": "Type of content", "How to order content": "How to order content", "Content": "Content", "List all content of specified type": "List all content of specified type", "Manage content manually": "Manage content manually", "Automatically update content with specified method": "Automatically update content with specified method", "Cache settings": "Cache settings", "Select cache provider and manually clear cache.": "Select cache provider and manually clear cache.", "Clear cache": "Clear cache", "\"File\" is the best option for most cases and should not be changed, unless you are familiar with another cache method and have it set up on the server already.": "\"File\" is the best option for most cases and should not be changed, unless you are familiar with another cache method and have it set up on the server already.", "Cache method": "Cache method", "Which method should be used for storing and retrieving cached items.": "Which method should be used for storing and retrieving cached items.", "File (Default)": "File (<PERSON><PERSON><PERSON>)", "Memcached host": "Memcached host", "Memcached port": "Memcached port", "Cache cleared": "<PERSON><PERSON> cleared", "Connected gmail account: :email": "Connected gmail account: :email", "Connect gmail account": "Connect gmail account", "Reconnect": "Reconnect", "Gmail account": "Gmail account", "Mailgun domain": "Mailgun domain", "Usually the domain of your site (site.com)": "Usually the domain of your site (site.com)", "Mailgun API key": "Mailgun API key", "Should start with `key-`": "Should start with `key-`", "Mailgun endpoint": "Mailgun endpoint", "Can be left empty, if your mailgun account is in the US region.": "Can be left empty, if your mailgun account is in the US region.", "Outgoing email settings": "Outgoing email settings", "Change outgoing email handlers, email credentials and other related settings.": "Change outgoing email handlers, email credentials and other related settings.", "From address": "From address", "All outgoing application emails will be sent from this email address.": "All outgoing application emails will be sent from this email address.", "From name": "From name", "All outgoing application emails will be sent using this name.": "All outgoing application emails will be sent using this name.", "Your selected mail method must be authorized to send emails using this address and name.": "Your selected mail method must be authorized to send emails using this address and name.", "Contact page address": "Contact page address", "Where emails from :contactPageUrl page should be sent to.": "Where emails from :contactPageUrl page should be sent to.", "Outgoing mail method": "Outgoing mail method", "Which method should be used for sending outgoing application emails (like registration confirmation)": "Which method should be used for sending outgoing application emails (like registration confirmation)", "Postmark token": "Postmark token", "SES key": "SES key", "SES secret": "SES secret", "SES region": "SES region", "SMTP host": "SMTP host", "SMTP username": "SMTP username", "SMTP password": "SMTP password", "SMTP port": "SMTP port", "SMTP encryption": "SMTP encryption", "Configure search method used on the site as well as related 3rd party integrations.": "Configure search method used on the site as well as related 3rd party integrations.", "Search method": "Search method", "Which method should be used for search related functionality across the site.": "Which method should be used for search related functionality across the site.", "MySQL mode": "MySQL mode", "Basic": "Basic", "Extended": "Extended", "Fulltext": "Fulltext", "Important!": "Important!", "<a>Meilisearch</a> needs to be installed and running for this method to work.": "<a>Meilisearch</a> needs to be installed and running for this method to work.", "<a>Elasticsearch</a> needs to be installed and running for this method to work.": "<a>Elasticsearch</a> needs to be installed and running for this method to work.", "Algolia app ID": "Algolia app ID", "Algolia app secret": "Algolia app secret", "Import records": "Import records", "Whenever a new search method is enabled, records that already exist in database need to be imported into the index. All records created after search method is enabled will be imported automatically.": "Whenever a new search method is enabled, records that already exist in database need to be imported into the index. All records created after search method is enabled will be imported automatically.", "Depending on number of records in database, importing could take some time. Don't close this window while it is in progress.": "Depending on number of records in database, importing could take some time. Don't close this window while it is in progress.", "What to import?": "What to import?", "Everything": "Everything", "Import now": "Import now", "Configure size and type of files that users are able to upload. This will affect all uploads across the site.": "Configure size and type of files that users are able to upload. This will affect all uploads across the site.", "File delivery optimization": "File delivery optimization", "Both X-Sendfile and X-Accel need to be enabled on the server first. When enabled, it will reduce server memory and CPU usage when previewing or downloading files, especially for large files.": "Both X-Sendfile and X-Accel need to be enabled on the server first. When enabled, it will reduce server memory and CPU usage when previewing or downloading files, especially for large files.", "X-Sendfile (Apache)": "X<PERSON><PERSON><PERSON><PERSON> (Apache)", "X-Accel (Nginx)": "X-Accel (Nginx)", "Chunk size": "Chunk size", "Size (in bytes) for each file chunk. It should only be changed if there is a maximum upload size on your server or proxy (for example cloudflare). If chunk size is larger then limit on the server, uploads will fail.": "Size (in bytes) for each file chunk. It should only be changed if there is a maximum upload size on your server or proxy (for example cloudflare). If chunk size is larger then limit on the server, uploads will fail.", "Maximum file size": "Maximum file size", "Maximum size (in bytes) for a single file user can upload.": "Maximum size (in bytes) for a single file user can upload.", "Available space": "Available space", "Disk space (in bytes) each user uploads are allowed to take up. This can be overridden per user.": "Disk space (in bytes) each user uploads are allowed to take up. This can be overridden per user.", "Allowed extensions": "Allowed extensions", "Add extension...": "Add extension...", "List of allowed file types (jpg, mp3, pdf etc.). Leave empty to allow all file types.": "List of allowed file types (jpg, mp3, pdf etc.). Leave empty to allow all file types.", "Blocked extensions": "Blocked extensions", "Prevent uploading of these file types, even if they are allowed above.": "Prevent uploading of these file types, even if they are allowed above.", "Maximum upload size on your server currently is set to <b>:size</b>": "Maximum upload size on your server currently is set to <b>:size</b>", "User Uploads Storage Method": "User Uploads Storage Method", "Where should user private file uploads be stored.": "Where should user private file uploads be stored.", "Local Disk (Default)": "Local Disk (Default)", "Public Uploads Storage Method": "Public Uploads Storage Method", "Where should user public uploads (like avatars) be stored.": "Where should user public uploads (like avatars) be stored.", "Amazon S3 key": "Amazon S3 key", "Amazon S3 secret": "Amazon S3 secret", "Amazon S3 region": "Amazon S3 region", "Amazon S3 bucket": "Amazon S3 bucket", "Amazon S3 endpoint": "Amazon S3 endpoint", "Only change endpoint if you are using another S3 compatible storage service.": "Only change endpoint if you are using another S3 compatible storage service.", "DigitalOcean key": "DigitalOcean key", "DigitalOcean secret": "DigitalOcean secret", "DigitalOcean region": "DigitalOcean region", "DigitalOcean bucket": "DigitalOcean bucket", "Backblaze KeyID": "Backblaze KeyID", "Backblaze applicationKey": "Backblaze applicationKey", "Backblaze Region": "Backblaze Region", "Backblaze bucket name": "Backblaze bucket name", "Upload files directly from the browser to s3 without going through the server. It will save on server bandwidth and should result in faster upload times. This should be enabled, unless storage provider does not support multipart uploads.": "Upload files directly from the browser to s3 without going through the server. It will save on server bandwidth and should result in faster upload times. This should be enabled, unless storage provider does not support multipart uploads.", "If s3 provider is not configured to allow uploads from browser, this can be done automatically via CORS button below, when valid credentials are saved.": "If s3 provider is not configured to allow uploads from browser, this can be done automatically via CORS button below, when valid credentials are saved.", "Direct upload": "Direct upload", "Configure CORS": "Configure CORS", "FTP hostname": "FTP hostname", "FTP username": "FTP username", "FTP password": "FTP password", "FTP directory": "FTP directory", "FTP port": "FTP port", "Passive": "Passive", "SSL": "SSL", "CORS file updated": "CORS file updated", "API access tokens": "API access tokens", "Documentation": "Documentation", "You have no personal access tokens yet": "You have no personal access tokens yet", "Last used": "Last used", "Never": "Never", "Create token": "Create token", "Delete token?": "Delete token?", "This token will be deleted immediately and permanently. Once deleted, it can no longer be used to make API requests.": "This token will be deleted immediately and permanently. Once deleted, it can no longer be used to make API requests.", "Token name": "Token name", "Create new token": "Create new token", "Done": "Done", "Make sure to store the token in a safe place. After this dialog is closed, token will not be viewable anymore.": "Make sure to store the token in a safe place. After this dialog is closed, token will not be viewable anymore.", "Token create": "Token create", "Token deleted": "<PERSON><PERSON> deleted", "Removed avatar": "Removed avatar", "Uploaded avatar": "Uploaded avatar", "Update name and profile image": "Update name and profile image", "Updated account details": "Updated account details", "Update password": "Update password", "Current password": "Current password", "Password changed": "Password changed", "If necessary, you may log out of all of your other browser sessions across all of your devices. Your recent sessions are listed below. If you feel your account has been compromised, you should also update your password.": "If necessary, you may log out of all of your other browser sessions across all of your devices. Your recent sessions are listed below. If you feel your account has been compromised, you should also update your password.", "Logged out other sessions.": "Logged out other sessions.", "Logout other sessions": "Logout other sessions", "This device": "This device", "API Token": "API Token", "Unknown IP": "Unknown IP", "Unknown": "Unknown", "Danger zone": "Danger zone", "Delete account?": "Delete account?", "Your account will be deleted immediately and permanently. Once deleted, accounts can not be restored.": "Your account will be deleted immediately and permanently. Once deleted, accounts can not be restored.", "Finish enabling two factor authentication.": "Finish enabling two factor authentication.", "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application or enter the setup key and provide the generated OTP code.": "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application or enter the setup key and provide the generated OTP code.", "Setup key: :key": "Setup key: :key", "You have not enabled two factor authentication.": "You have not enabled two factor authentication.", "Regenerate recovery codes": "Regenerate recovery codes", "Two factor authentication has been disabled.": "Two factor authentication has been disabled.", "You have enabled two factor authentication.": "You have enabled two factor authentication.", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.", "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.": "When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application.", "Mutation performed": "Mutation performed", "Reset to original": "Reset to original", "Edit SEO meta tags": "Edit SEO meta tags", "Updated SEO tags": "Updated SEO tags", "Dropbox application key": "Dropbox application key", "Dropbox application secret": "Dropbox application secret", "Dropbox refresh token": "Dropbox refresh token", "Get dropbox refresh token": "Get dropbox refresh token", "Connected dropbox account": "Connected dropbox account", "Click the 'get access code' button to get dropbox access code, then paste it into the field below.": "Click the 'get access code' button to get dropbox access code, then paste it into the field below.", "Get access code": "Get access code", "Dropbox access code": "Dropbox access code", "Imported search models": "Imported search models", "Today": "Today", "Enter a date after :date": "Enter a date after :date", "Enter a date before :date": "Enter a date before :date", "Preceding period": "Preceding period", "Same period last year": "Same period last year", "Compare": "Compare", "Yesterday": "Yesterday", "This week": "This week", "Last week": "Last week", "Last 7 days": "Last 7 days", "Last 30 days": "Last 30 days", "Last 3 months": "Last 3 months", "Last 12 months": "Last 12 months", "This month": "This month", "This year": "This year", "Last year": "Last year", "All Files": "All Files", "This is not a valid date.": "This is not a valid date.", "Folder with same name already exists.": "Folder with same name already exists.", "Destination must be a folder": "Destination must be a folder", "Files shared on :siteName": "Files shared on :siteName", "View now": "View now", ":username shared a file with you": ":username shared a file with you", ":username has shared :count files with you": ":username has shared :count files with you", "New files": "New files", "New folders": "New folders", "New users": "New users", "Total Space Used": "Total Space Used", "Could not persist settings.": "Could not persist settings.", "Could not validate selected optimization. Is it enabled on the server?": "Could not validate selected optimization. Is it enabled on the server?", "These credentials are not valid": "These credentials are not valid", ":username replied to your comment:": ":username replied to your comment:", "View user": "View user", "on": "on", "Current site url can't be attached as custom domain.": "Current site url can't be attached as custom domain.", "Could not validate domain.": "Could not validate domain.", "$value can't be used as a branded domain.": "$value can't be used as a branded domain.", "There was an issue. Please try again later.": "There was an issue. Please try again later.", "You don't have required permissions for this action.": "You don't have required permissions for this action.", "This domain is blacklisted.": "This domain is blacklisted.", "The provided password does not match your current password.": "The provided password does not match your current password.", "Banned: :reason": "Banned: :reason", "This user is banned.": "This user is banned.", "Could not attach specified users to role.": "Could not attach specified users to role.", "Could not delete currently logged in user: :email": "Could not delete currently logged in user: :email", "Could not delete admin user: :email": "Could not delete admin user: :email", "The security code you entered is invalid or has expired": "The security code you entered is invalid or has expired", "Specified credentials are not valid": "Specified credentials are not valid", "This password is not correct.": "This password is not correct.", "Could not retrieve social sign in account.": "Could not retrieve social sign in account.", "Your :site security code is:": "Your :site security code is:", "Your :site security code is :code": "Your :site security code is :code", "If you did not request this code, please go to your :link and change your password right away.": "If you did not request this code, please go to your :link and change your password right away.", "This code will expire in :minutes minutes.": "This code will expire in :minutes minutes.", "There must be at least one localization.": "There must be at least one localization.", "Locale code is required": "Locale code is required", "Invoice": "Invoice", "Invoice ID": "Invoice ID", "Invoice Date": "Invoice Date", "Billed To": "Billed To", "Qty": "Qty", "Subscription Dues": "Subscription Dues", "plan": "plan", "Total": "Total", "Notes": "Notes", "You are already logged in as this user.": "You are already logged in as this user.", "Current period": "Current period", "Previous period": "Previous period", "Sessions": "Sessions", "Others": "Others", "Other": "Other", ":inviter invited you to :siteName :workspace": ":inviter invited you to :siteName :workspace", "Join your :workspace teammates on :siteName": "Join your :workspace teammates on :siteName", "Join your team": "Join your team", "This invitation link will expire in 3 days.": "This invitation link will expire in 3 days.", "If you do not wish to join this workspace, no further action is required.": "If you do not wish to join this workspace, no further action is required.", ":inviter invited you to join `:workspace.`": ":inviter invited you to join `:workspace.`", "Accepting the invitation will give you access to links, domains, overlays and other resources in this workspace.": "Accepting the invitation will give you access to links, domains, overlays and other resources in this workspace.", "This download link will only work if you are logged in as user who has requested the export and it will expire in one day.": "This download link will only work if you are logged in as user who has requested the export and it will expire in one day.", "This download link will expire in one day.": "This download link will expire in one day.", "“:name“ CSV export is ready to download.": "“:name“ CSV export is ready to download.", "Files of this type are not allowed": "Files of this type are not allowed", "The file size may not be greater than :size": "The file size may not be greater than :size", "New message via :siteName contact page.": "New message via :siteName contact page.", "New message via :siteName contact page from ':userEmail'": "New message via :siteName contact page from ':userEmail'", "Unable to resume subscription that is not within grace period.": "Unable to resume subscription that is not within grace period.", "Could not delete ':plan": "Could not delete ':plan", "This subscription ID already exists": "This subscription ID already exists", "Hello, :name": "Hello, :name", "View subscription": "View subscription", "Payment for :name subscription failed": "Payment for :name subscription failed", "We could not charge your specified payment method for :planName. We will retry it one more time, after which time your subscription on :siteName will be cancelled and you will lose associated benefits.": "We could not charge your specified payment method for :planName. We will retry it one more time, after which time your subscription on :siteName will be cancelled and you will lose associated benefits.", "View receipt": "View receipt", ":name payment receipt": ":name payment receipt", "This is a receipt for your latest :siteName payment.": "This is a receipt for your latest :siteName payment.", "Invalid emails: :emails": "Invalid emails: :emails", "User with email: :emails does not exist": "User with email: :emails does not exist", "You have already submitted a report for this item.": "You have already submitted a report for this item.", "Auto update method is required.": "Auto update method is required.", "All rights reserved.": "All rights reserved.", "Whoops!": "Whoops!", "Hello!": "Hello!", "Regards": "Regards", "If you’re having trouble clicking the \":actionText\" button, copy and paste the URL below into your web browser:": "If you’re having trouble clicking the \":actionText\" button, copy and paste the URL below into your web browser:", "Passwords must be at least six characters and match the confirmation.": "Passwords must be at least six characters and match the confirmation.", "We have e-mailed your password reset link!": "We have e-mailed your password reset link!", "This password reset token is invalid.": "This password reset token is invalid.", "We can't find a user with that e-mail address.": "We can't find a user with that e-mail address.", "These credentials do not match our records.": "These credentials do not match our records.", "Too many login attempts. Please try again in :seconds seconds.": "Too many login attempts. Please try again in :seconds seconds.", "The :attribute must be accepted.": "The :attribute must be accepted.", "The :attribute is not a valid URL.": "The :attribute is not a valid URL.", "The :attribute must be a date after :date.": "The :attribute must be a date after :date.", "The :attribute must be a date after or equal to :date.": "The :attribute must be a date after or equal to :date.", "The :attribute may only contain letters.": "The :attribute may only contain letters.", "The :attribute may only contain letters, numbers, and dashes.": "The :attribute may only contain letters, numbers, and dashes.", "The :attribute may only contain letters and numbers.": "The :attribute may only contain letters and numbers.", "The :attribute must be an array.": "The :attribute must be an array.", "The :attribute must be a date before :date.": "The :attribute must be a date before :date.", "The :attribute must be a date before or equal to :date.": "The :attribute must be a date before or equal to :date.", "The :attribute must be between :min and :max.": "The :attribute must be between :min and :max.", "The :attribute must be between :min and :max kilobytes.": "The :attribute must be between :min and :max kilobytes.", "The :attribute must be between :min and :max characters.": "The :attribute must be between :min and :max characters.", "The :attribute must have between :min and :max items.": "The :attribute must have between :min and :max items.", "The :attribute field must be true or false.": "The :attribute field must be true or false.", "The :attribute confirmation does not match.": "The :attribute confirmation does not match.", "The :attribute is not a valid date.": "The :attribute is not a valid date.", "The :attribute does not match the format :format.": "The :attribute does not match the format :format.", "The :attribute and :other must be different.": "The :attribute and :other must be different.", "The :attribute must be :digits digits.": "The :attribute must be :digits digits.", "The :attribute must be between :min and :max digits.": "The :attribute must be between :min and :max digits.", "The :attribute has invalid image dimensions.": "The :attribute has invalid image dimensions.", "The :attribute field has a duplicate value.": "The :attribute field has a duplicate value.", "The :attribute must be a valid email address.": "The :attribute must be a valid email address.", "The selected :attribute is invalid.": "The selected :attribute is invalid.", "The :attribute must be a file.": "The :attribute must be a file.", "The :attribute field must have a value.": "The :attribute field must have a value.", "The :attribute must be an image.": "The :attribute must be an image.", "The :attribute field does not exist in :other.": "The :attribute field does not exist in :other.", "The :attribute must be an integer.": "The :attribute must be an integer.", "The :attribute must be a valid IP address.": "The :attribute must be a valid IP address.", "The :attribute must be a valid IPv4 address.": "The :attribute must be a valid IPv4 address.", "The :attribute must be a valid IPv6 address.": "The :attribute must be a valid IPv6 address.", "The :attribute must be a valid JSON string.": "The :attribute must be a valid JSON string.", "The :attribute may not be greater than :max.": "The :attribute may not be greater than :max.", "The :attribute may not be greater than :max kilobytes.": "The :attribute may not be greater than :max kilobytes.", "The :attribute may not be greater than :max characters.": "The :attribute may not be greater than :max characters.", "The :attribute may not have more than :max items.": "The :attribute may not have more than :max items.", "The :attribute must be a file of type: :values.": "The :attribute must be a file of type: :values.", "The :attribute must be at least :min.": "The :attribute must be at least :min.", "The :attribute must be at least :min kilobytes.": "The :attribute must be at least :min kilobytes.", "The :attribute must be at least :min characters.": "The :attribute must be at least :min characters.", "The :attribute must have at least :min items.": "The :attribute must have at least :min items.", "The :attribute must be a number.": "The :attribute must be a number.", "The :attribute field must be present.": "The :attribute field must be present.", "The :attribute format is invalid.": "The :attribute format is invalid.", "The :attribute field is required.": "The :attribute field is required.", "The :attribute field is required when :other is :value.": "The :attribute field is required when :other is :value.", "The :attribute field is required unless :other is in :values.": "The :attribute field is required unless :other is in :values.", "The :attribute field is required when :values is present.": "The :attribute field is required when :values is present.", "The :attribute field is required when :values is not present.": "The :attribute field is required when :values is not present.", "The :attribute field is required when none of :values are present.": "The :attribute field is required when none of :values are present.", "The :attribute and :other must match.": "The :attribute and :other must match.", "The :attribute must be :size.": "The :attribute must be :size.", "The :attribute must be :size kilobytes.": "The :attribute must be :size kilobytes.", "The :attribute must be :size characters.": "The :attribute must be :size characters.", "The :attribute must contain :size items.": "The :attribute must contain :size items.", "The :attribute must be a string.": "The :attribute must be a string.", "The :attribute must be a valid zone.": "The :attribute must be a valid zone.", "The :attribute has already been taken.": "The :attribute has already been taken.", "The :attribute failed to upload.": "The :attribute failed to upload.", "Current password is incorrect.": "Current password is incorrect.", "Please confirm your email address.": "Please confirm your email address.", "User with email \":attribute\" does not exist.": "User with email \":attribute\" does not exist.", "This is not a valid date format.": "This is not a valid date format.", "name": "name", "message": "message", "The provided two factor recovery code was invalid.": "The provided two factor recovery code was invalid.", "The provided two factor authentication code was invalid.": "The provided two factor authentication code was invalid.", "The provided password was incorrect.": "The provided password was incorrect.", "The :attribute must be at least :length characters and contain at least one uppercase character.": "The :attribute must be at least :length characters and contain at least one uppercase character.", "The :attribute must be at least :length characters and contain at least one number.": "The :attribute must be at least :length characters and contain at least one number.", "The :attribute must be at least :length characters and contain at least one special character.": "The :attribute must be at least :length characters and contain at least one special character.", "The :attribute must be at least :length characters and contain at least one uppercase character and one number.": "The :attribute must be at least :length characters and contain at least one uppercase character and one number.", "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.": "The :attribute must be at least :length characters and contain at least one uppercase character and one special character.", "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.": "The :attribute must be at least :length characters and contain at least one uppercase character, one number, and one special character.", "The :attribute must be at least :length characters and contain at least one special character and one number.": "The :attribute must be at least :length characters and contain at least one special character and one number.", "The :attribute must be at least :length characters.": "The :attribute must be at least :length characters.", "Showing": "Showing", "to": "to", "of": "of", "results": "results", "Pagination Navigation": "Pagination Navigation", "Go to page :page": "Go to page :page", "Reset Password Notification": "Reset Password Notification", "You are receiving this email because we received a password reset request for your account.": "You are receiving this email because we received a password reset request for your account.", "This password reset link will expire in :count minutes.": "This password reset link will expire in :count minutes.", "If you did not request a password reset, no further action is required.": "If you did not request a password reset, no further action is required.", "Verify Email Address": "Verify Em<PERSON> Address", "Please click the button below to verify your email address.": "Please click the button below to verify your email address.", "If you did not create an account, no further action is required.": "If you did not create an account, no further action is required.", "Payment Required": "Payment Required", "Not Found": "Not Found", "Server Error": "Server Error", "Forbidden": "Forbidden", "Too Many Requests": "Too Many Requests", "Page Expired": "Page Expired", "Unauthorized": "Unauthorized", "Service Unavailable": "Service Unavailable", "Privacy Policy": "Privacy Policy", "Terms of Service": "Terms of Service", "Contact Us": "Contact Us", "Admin Area": "Admin Area", "My Files": "My Files", "Pages": "Pages", "Files": "Files", "Logs": "Logs", "Access Admin": "Access Admin", "admin": "admin", "Required in order to access any admin area page.": "Required in order to access any admin area page.", "Update Appearance": "Update Appearance", "Allows access to appearance editor.": "Allows access to appearance editor.", "Super Admin": "Super Admin", "Give all permissions to user.": "Give all permissions to user.", "Access Api": "Access Api", "api": "api", "Required in order for users to be able to use the API.": "Required in order for users to be able to use the API.", "View Roles": "View Roles", "roles": "roles", "Allow viewing ALL roles, regardless of who is the owner.": "Allow viewing ALL roles, regardless of who is the owner.", "Create Roles": "Create Roles", "Allow creating new roles, regardless of who is the owner.": "Allow creating new roles, regardless of who is the owner.", "Update Roles": "Update Roles", "Allow updating ALL roles, regardless of who is the owner.": "Allow updating ALL roles, regardless of who is the owner.", "Delete Roles": "Delete Roles", "Allow deleting ALL roles, regardless of who is the owner.": "Allow deleting ALL roles, regardless of who is the owner.", "View Custom Pages": "View Custom Pages", "custom_pages": "custom_pages", "Allow viewing of all pages on the site, regardless of who created them. User can view their own pages without this permission.": "Allow viewing of all pages on the site, regardless of who created them. User can view their own pages without this permission.", "Create Custom Pages": "Create Custom Pages", "Allow creating new custom pages, regardless of who is the owner.": "Allow creating new custom pages, regardless of who is the owner.", "Count": "Count", "Maximum number of pages user will be able to create. Leave empty for unlimited.": "Maximum number of pages user will be able to create. Leave empty for unlimited.", "Update Custom Pages": "Update Custom Pages", "Allow editing of all pages on the site, regardless of who created them. User can edit their own pages without this permission.": "Allow editing of all pages on the site, regardless of who created them. User can edit their own pages without this permission.", "Delete Custom Pages": "Delete Custom Pages", "Allow deleting of all pages on the site, regardless of who created them. User can delete their own pages without this permission.": "Allow deleting of all pages on the site, regardless of who created them. User can delete their own pages without this permission.", "View Files": "View Files", "files": "files", "Allow viewing all uploaded files on the site. Users can view their own uploads without this permission.": "Allow viewing all uploaded files on the site. Users can view their own uploads without this permission.", "Create Files": "Create Files", "Allow uploading files on the site. This permission is used by any page where it is possible for user to upload files.": "Allow uploading files on the site. This permission is used by any page where it is possible for user to upload files.", "Update Files": "Update Files", "Allow editing all uploaded files on the site. Users can edit their own uploads without this permission.": "Allow editing all uploaded files on the site. Users can edit their own uploads without this permission.", "Delete Files": "Delete Files", "Allow deleting all uploaded files on the site. Users can delete their own uploads (where applicable) without this permission.": "Allow deleting all uploaded files on the site. Users can delete their own uploads (where applicable) without this permission.", "Download Files": "Download Files", "Allow downloading all uploaded files on the site. Users can download their own uploads (where applicable) without this permission.": "Allow downloading all uploaded files on the site. Users can download their own uploads (where applicable) without this permission.", "View Users": "View Users", "users": "users", "Allow viewing user profile pages on the site. User can view their own profile without this permission.": "Allow viewing user profile pages on the site. User can view their own profile without this permission.", "Create Users": "Create Users", "Allow creating users from admin area. Users can register for new accounts without this permission. Registration can be disabled from settings page.": "Allow creating users from admin area. Users can register for new accounts without this permission. Registration can be disabled from settings page.", "Update Users": "Update Users", "Allow editing details of any user on the site. User can edit their own details without this permission.": "Allow editing details of any user on the site. User can edit their own details without this permission.", "Delete Users": "Delete Users", "Allow deleting any user on the site. User can request deletion of their own account without this permission.": "Allow deleting any user on the site. User can request deletion of their own account without this permission.", "View Localizations": "View Localizations", "localizations": "localizations", "Allow viewing ALL localizations, regardless of who is the owner.": "Allow viewing ALL localizations, regardless of who is the owner.", "Create Localizations": "Create Localizations", "Allow creating new localizations, regardless of who is the owner.": "Allow creating new localizations, regardless of who is the owner.", "Update Localizations": "Update Localizations", "Allow updating ALL localizations, regardless of who is the owner.": "Allow updating ALL localizations, regardless of who is the owner.", "Delete Localizations": "Delete Localizations", "Allow deleting ALL localizations, regardless of who is the owner.": "Allow deleting ALL localizations, regardless of who is the owner.", "View Settings": "View Settings", "settings": "settings", "Allow viewing ALL settings, regardless of who is the owner.": "Allow viewing ALL settings, regardless of who is the owner.", "Update Settings": "Update Settings", "Allow updating ALL settings, regardless of who is the owner.": "Allow updating ALL settings, regardless of who is the owner.", "View Plans": "View Plans", "plans": "plans", "Allow viewing ALL plans, regardless of who is the owner.": "Allow viewing ALL plans, regardless of who is the owner.", "Create Plans": "Create Plans", "Allow creating new plans, regardless of who is the owner.": "Allow creating new plans, regardless of who is the owner.", "Update Plans": "Update Plans", "Allow updating ALL plans, regardless of who is the owner.": "Allow updating ALL plans, regardless of who is the owner.", "Delete Plans": "Delete Plans", "Allow deleting ALL plans, regardless of who is the owner.": "Allow deleting ALL plans, regardless of who is the owner.", "View Invoices": "View Invoices", "invoices": "invoices", "Allow viewing ALL invoices, regardless of who is the owner.": "Allow viewing ALL invoices, regardless of who is the owner.", "View Tags": "View Tags", "tags": "tags", "Allow viewing ALL tags, regardless of who is the owner.": "Allow viewing ALL tags, regardless of who is the owner.", "Create Tags": "Create Tags", "Allow creating new tags, regardless of who is the owner.": "Allow creating new tags, regardless of who is the owner.", "Update Tags": "Update Tags", "Allow updating ALL tags, regardless of who is the owner.": "Allow updating ALL tags, regardless of who is the owner.", "Delete Tags": "Delete Tags", "Allow deleting ALL tags, regardless of who is the owner.": "Allow deleting ALL tags, regardless of who is the owner.", "View Workspaces": "View Workspaces", "workspaces": "workspaces", "Allow viewing ALL workspaces, regardless of who is the owner.": "Allow viewing ALL workspaces, regardless of who is the owner.", "Create Workspaces": "Create Workspaces", "Allow creating new workspaces, regardless of who is the owner.": "Allow creating new workspaces, regardless of who is the owner.", "Maximum number of workspaces user will be able to create. Leave empty for unlimited.": "Maximum number of workspaces user will be able to create. Leave empty for unlimited.", "Member count": "Member count", "Maximum number of members workspace is allowed to have.": "Maximum number of members workspace is allowed to have.", "Update Workspaces": "Update Workspaces", "Allow updating ALL workspaces, regardless of who is the owner.": "Allow updating ALL workspaces, regardless of who is the owner.", "Delete Workspaces": "Delete Workspaces", "Allow deleting ALL workspaces, regardless of who is the owner.": "Allow deleting ALL workspaces, regardless of who is the owner.", "View Links": "View Links", "links": "links", "Allow viewing ALL links, regardless of who is the owner.": "Allow viewing ALL links, regardless of who is the owner.", "Create Links": "Create Links", "Allow creating new links, regardless of who is the owner.": "Allow creating new links, regardless of who is the owner.", "Update Links": "Update Links", "Allow updating ALL links, regardless of who is the owner.": "Allow updating ALL links, regardless of who is the owner.", "Delete Links": "Delete Links", "Allow deleting ALL links, regardless of who is the owner.": "Allow deleting ALL links, regardless of who is the owner.", "Subscribe Notifications": "Subscribe Notifications", "notifications": "notifications", "Allows agents to subscribe to various conversation notifications.": "Allows agents to subscribe to various conversation notifications."}